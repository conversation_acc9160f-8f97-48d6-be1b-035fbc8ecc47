#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script de debug para conversão RFA → OBJ
"""

import sys
import os
import clr

# Adicionar referências do Revit
clr.AddReference('RevitAPI')
clr.AddReference('RevitAPIUI')

from Autodesk.Revit.DB import *
from pyrevit import revit

def main():
    try:
        print("=== DEBUG CONVERTER ===")
        
        # Obter argumentos
        args = sys.argv
        print("Argumentos: " + str(args))
        
        if len(args) < 2:
            print("ERRO: Arquivo RFA não especificado")
            return 1
        
        input_file = args[1]
        print("Arquivo RFA: " + input_file)
        
        # Verificar se arquivo existe
        if not os.path.exists(input_file):
            print("ERRO: Arquivo não encontrado")
            return 1
        
        print("Arquivo encontrado")
        
        # Obter aplicação do Revit
        app = revit.app
        if app is None:
            print("ERRO: Aplicação Revit não disponível")
            return 1
        
        print("Aplicação Revit obtida: " + app.VersionName)
        
        # Abrir documento de família
        print("Abrindo documento de família...")
        family_doc = app.OpenDocumentFile(input_file)
        
        if family_doc is None:
            print("ERRO: Falha ao abrir documento de família")
            return 1
        
        print("Documento de família aberto: " + family_doc.Title)
        
        # Verificar se é família
        if not family_doc.IsFamilyDocument:
            print("ERRO: Não é um documento de família")
            family_doc.Close(False)
            return 1
        
        print("Documento é uma família válida")
        
        # Fechar documento de família
        family_title = family_doc.Title
        family_doc.Close(False)
        print("Documento de família fechado")
        
        # Criar novo documento de projeto
        print("Criando documento de projeto...")
        try:
            project_doc = app.NewProjectDocument(UnitSystem.Metric)
            if project_doc is None:
                print("ERRO: Falha ao criar documento de projeto")
                return 1
            print("Documento de projeto criado: " + project_doc.Title)
        except Exception as e:
            print("ERRO ao criar documento de projeto: " + str(e))
            return 1
        
        # Carregar família no projeto
        print("Carregando família no projeto...")
        try:
            with Transaction(project_doc, "Carregar Família") as trans:
                trans.Start()
                
                loaded_family = None
                load_result = project_doc.LoadFamily(input_file, loaded_family)
                
                if load_result and loaded_family is not None:
                    trans.Commit()
                    print("Família carregada: " + loaded_family.Name)
                else:
                    trans.RollBack()
                    print("ERRO: Falha ao carregar família")
                    project_doc.Close(False)
                    return 1
        except Exception as e:
            print("ERRO ao carregar família: " + str(e))
            project_doc.Close(False)
            return 1
        
        # Inserir instância na origem
        print("Inserindo instância na origem...")
        try:
            # Obter primeiro símbolo da família
            family_symbol_ids = loaded_family.GetFamilySymbolIds()
            if len(family_symbol_ids) == 0:
                print("ERRO: Nenhum símbolo encontrado")
                project_doc.Close(False)
                return 1
            
            family_symbol_id = list(family_symbol_ids)[0]
            family_symbol = project_doc.GetElement(family_symbol_id)
            
            with Transaction(project_doc, "Inserir Família") as trans:
                trans.Start()
                
                # Ativar símbolo se necessário
                if not family_symbol.IsActive:
                    family_symbol.Activate()
                    project_doc.Regenerate()
                
                # Criar instância na origem
                origin_point = XYZ(0, 0, 0)
                family_instance = project_doc.Create.NewFamilyInstance(
                    origin_point,
                    family_symbol,
                    StructuralType.NonStructural
                )
                
                if family_instance is not None:
                    trans.Commit()
                    print("Instância criada: ID " + str(family_instance.Id))
                else:
                    trans.RollBack()
                    print("ERRO: Falha ao criar instância")
                    project_doc.Close(False)
                    return 1
        except Exception as e:
            print("ERRO ao inserir instância: " + str(e))
            project_doc.Close(False)
            return 1
        
        # Criar arquivo OBJ simples
        output_file = os.path.splitext(input_file)[0] + ".obj"
        print("Criando arquivo OBJ: " + output_file)
        
        try:
            with open(output_file, 'w') as f:
                f.write("# BIMEX Debug Converter\n")
                f.write("# Família: " + family_title + "\n")
                f.write("# Teste de conversão via modelo de projeto\n\n")
                f.write("# Placeholder vertex\n")
                f.write("v 0.0 0.0 0.0\n")
                f.write("v 1.0 0.0 0.0\n")
                f.write("v 0.0 1.0 0.0\n")
                f.write("\n")
                f.write("# Placeholder face\n")
                f.write("f 1 2 3\n")
            
            print("Arquivo OBJ criado com sucesso!")
            
            # Verificar tamanho
            file_size = os.path.getsize(output_file)
            print("Tamanho do arquivo: " + str(file_size) + " bytes")
            
        except Exception as e:
            print("ERRO ao criar arquivo OBJ: " + str(e))
            project_doc.Close(False)
            return 1
        
        # Fechar documento de projeto
        project_doc.Close(False)
        print("Documento de projeto fechado")
        
        print("=== DEBUG CONCLUÍDO COM SUCESSO ===")
        return 0
        
    except Exception as e:
        print("ERRO durante debug: " + str(e))
        return 1

if __name__ == "__main__":
    sys.exit(main())
